package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.Map;

@RequestMapping("/api/v1/gestion-tramite/documentos")
public interface GestionTramiteDocumentalAPI {
    @PostMapping(path = "/recepcion", consumes = "application/json")
    ResponseEntity<Object> recibirDocumento(Map<String, Object> request);
}
