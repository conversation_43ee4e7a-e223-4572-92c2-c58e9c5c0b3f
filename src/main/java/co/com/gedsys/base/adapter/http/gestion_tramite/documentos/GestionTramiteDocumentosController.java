package co.com.gedsys.base.adapter.http.gestion_tramite.documentos;

import co.com.gedsys.base.adapter.http.produccion.documentos.SolicitudRecepcionDocumento;
import co.com.gedsys.base.util.SchemeHomologator;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
public class GestionTramiteDocumentosController implements GestionTramiteDocumentalAPI {
    private final SchemeHomologator<SolicitudRecepcionDocumento> schemeHomologator;

    public GestionTramiteDocumentosController() {
        ObjectMapper objectMapper = new ObjectMapper();
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        schemeHomologator = new SchemeHomologator<SolicitudRecepcionDocumento>(objectMapper, validator)
                .withSchemaClass(SolicitudRecepcionDocumento.class);
    }

    @Override
    public ResponseEntity<Object> recibirDocumento(Map<String, Object> request) {
        var solicitud = schemeHomologator.validateAndConvertToSchema(request);
        return ResponseEntity.ok(solicitud.toString());
    }
}
